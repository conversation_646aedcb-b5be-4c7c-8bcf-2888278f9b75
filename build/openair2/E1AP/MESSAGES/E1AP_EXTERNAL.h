/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "ASN1C-UsefulInformationObjectClasses"
 * 	found in "/opt/asn1c/share/asn1c/standard-modules/ASN1C-UsefulInformationObjectClasses.asn1"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_EXTERNAL_H_
#define	_E1AP_EXTERNAL_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OBJECT_IDENTIFIER.h>
#include <NativeInteger.h>
#include <ObjectDescriptor.h>
#include <ANY.h>
#include <OCTET_STRING.h>
#include <BIT_STRING.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_EXTERNAL__encoding_PR {
	E1AP_EXTERNAL__encoding_PR_NOTHING,	/* No components present */
	E1AP_EXTERNAL__encoding_PR_single_ASN1_type,
	E1AP_EXTERNAL__encoding_PR_octet_aligned,
	E1AP_EXTERNAL__encoding_PR_arbitrary
} E1AP_EXTERNAL__encoding_PR;

/* E1AP_EXTERNAL */
typedef struct E1AP_EXTERNAL {
	OBJECT_IDENTIFIER_t	*direct_reference;	/* OPTIONAL */
	long	*indirect_reference;	/* OPTIONAL */
	ObjectDescriptor_t	*data_value_descriptor;	/* OPTIONAL */
	struct E1AP_EXTERNAL__encoding {
		E1AP_EXTERNAL__encoding_PR present;
		union E1AP_EXTERNAL__E1AP_encoding_u {
			ANY_t	 single_ASN1_type;
			OCTET_STRING_t	 octet_aligned;
			BIT_STRING_t	 arbitrary;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} encoding;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EXTERNAL_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EXTERNAL;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_EXTERNAL_H_ */
#include <asn_internal.h>
