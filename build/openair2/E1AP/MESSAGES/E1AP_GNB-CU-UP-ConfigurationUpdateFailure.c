/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-PDU-Contents"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_GNB-CU-UP-ConfigurationUpdateFailure.h"

asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_GNB_CU_UP_ConfigurationUpdateFailure, protocolIEs),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_E1AP_ProtocolIE_Container_4932P11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"protocolIEs"
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* protocolIEs */
};
asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_specs_1 = {
	sizeof(struct E1AP_GNB_CU_UP_ConfigurationUpdateFailure),
	offsetof(struct E1AP_GNB_CU_UP_ConfigurationUpdateFailure, _asn_ctx),
	asn_MAP_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tag2el_1,
	1,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure = {
	"GNB-CU-UP-ConfigurationUpdateFailure",
	"GNB-CU-UP-ConfigurationUpdateFailure",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1,
	sizeof(asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1)
		/sizeof(asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1[0]), /* 1 */
	asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1)
		/sizeof(asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_1,
	1,	/* Elements count */
	&asn_SPC_E1AP_GNB_CU_UP_ConfigurationUpdateFailure_specs_1	/* Additional specs */
};

