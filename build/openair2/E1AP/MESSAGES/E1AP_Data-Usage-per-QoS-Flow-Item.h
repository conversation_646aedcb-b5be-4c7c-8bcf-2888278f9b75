/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_Data_Usage_per_QoS_Flow_Item_H_
#define	_E1AP_Data_Usage_per_QoS_Flow_Item_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_QoS-Flow-Identifier.h"
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_Data_Usage_per_QoS_Flow_Item__secondaryRATType {
	E1AP_Data_Usage_per_QoS_Flow_Item__secondaryRATType_nR	= 0,
	E1AP_Data_Usage_per_QoS_Flow_Item__secondaryRATType_e_UTRA	= 1
	/*
	 * Enumeration is extensible
	 */
} e_E1AP_Data_Usage_per_QoS_Flow_Item__secondaryRATType;

/* Forward declarations */
struct E1AP_ProtocolExtensionContainer;
struct E1AP_MRDC_Data_Usage_Report_Item;

/* E1AP_Data-Usage-per-QoS-Flow-Item */
typedef struct E1AP_Data_Usage_per_QoS_Flow_Item {
	E1AP_QoS_Flow_Identifier_t	 qoS_Flow_Identifier;
	long	 secondaryRATType;
	struct E1AP_Data_Usage_per_QoS_Flow_Item__qoS_Flow_Timed_Report_List {
		A_SEQUENCE_OF(struct E1AP_MRDC_Data_Usage_Report_Item) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} qoS_Flow_Timed_Report_List;
	struct E1AP_ProtocolExtensionContainer	*iE_Extensions;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Data_Usage_per_QoS_Flow_Item_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_E1AP_secondaryRATType_3;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Data_Usage_per_QoS_Flow_Item;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Data_Usage_per_QoS_Flow_Item_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_Data_Usage_per_QoS_Flow_Item_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_Data_Usage_per_QoS_Flow_Item_H_ */
#include <asn_internal.h>
