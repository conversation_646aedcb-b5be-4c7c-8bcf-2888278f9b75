/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-PDU-Descriptions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_E1AP_PDU_H_
#define	_E1AP_E1AP_PDU_H_


#include <asn_application.h>

/* Including external dependencies */
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_E1AP_PDU_PR {
	E1AP_E1AP_PDU_PR_NOTHING,	/* No components present */
	E1AP_E1AP_PDU_PR_initiatingMessage,
	E1AP_E1AP_PDU_PR_successfulOutcome,
	E1AP_E1AP_PDU_PR_unsuccessfulOutcome
	/* Extensions may appear below */
	
} E1AP_E1AP_PDU_PR;

/* Forward declarations */
struct E1AP_InitiatingMessage;
struct E1AP_SuccessfulOutcome;
struct E1AP_UnsuccessfulOutcome;

/* E1AP_E1AP-PDU */
typedef struct E1AP_E1AP_PDU {
	E1AP_E1AP_PDU_PR present;
	union E1AP_E1AP_PDU_u {
		struct E1AP_InitiatingMessage	*initiatingMessage;
		struct E1AP_SuccessfulOutcome	*successfulOutcome;
		struct E1AP_UnsuccessfulOutcome	*unsuccessfulOutcome;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_E1AP_PDU_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_E1AP_PDU;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_E1AP_PDU_H_ */
#include <asn_internal.h>
