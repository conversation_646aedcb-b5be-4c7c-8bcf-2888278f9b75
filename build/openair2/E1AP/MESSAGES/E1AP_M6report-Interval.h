/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_M6report_Interval_H_
#define	_E1AP_M6report_Interval_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_M6report_Interval {
	E1AP_M6report_Interval_ms120	= 0,
	E1AP_M6report_Interval_ms240	= 1,
	E1AP_M6report_Interval_ms480	= 2,
	E1AP_M6report_Interval_ms640	= 3,
	E1AP_M6report_Interval_ms1024	= 4,
	E1AP_M6report_Interval_ms2048	= 5,
	E1AP_M6report_Interval_ms5120	= 6,
	E1AP_M6report_Interval_ms10240	= 7,
	E1AP_M6report_Interval_ms20480	= 8,
	E1AP_M6report_Interval_ms40960	= 9,
	E1AP_M6report_Interval_min1	= 10,
	E1AP_M6report_Interval_min6	= 11,
	E1AP_M6report_Interval_min12	= 12,
	E1AP_M6report_Interval_min30	= 13
	/*
	 * Enumeration is extensible
	 */
} e_E1AP_M6report_Interval;

/* E1AP_M6report-Interval */
typedef long	 E1AP_M6report_Interval_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_E1AP_M6report_Interval_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_M6report_Interval;
extern const asn_INTEGER_specifics_t asn_SPC_E1AP_M6report_Interval_specs_1;
asn_struct_free_f E1AP_M6report_Interval_free;
asn_struct_print_f E1AP_M6report_Interval_print;
asn_constr_check_f E1AP_M6report_Interval_constraint;
xer_type_decoder_f E1AP_M6report_Interval_decode_xer;
xer_type_encoder_f E1AP_M6report_Interval_encode_xer;
per_type_decoder_f E1AP_M6report_Interval_decode_uper;
per_type_encoder_f E1AP_M6report_Interval_encode_uper;
per_type_decoder_f E1AP_M6report_Interval_decode_aper;
per_type_encoder_f E1AP_M6report_Interval_encode_aper;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_M6report_Interval_H_ */
#include <asn_internal.h>
