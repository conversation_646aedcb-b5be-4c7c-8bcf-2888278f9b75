/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_DRB_Removed_Item_H_
#define	_E1AP_DRB_Removed_Item_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_DRB-ID.h"
#include <NativeEnumerated.h>
#include <OCTET_STRING.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_DRB_Removed_Item__dRB_Released_In_Session {
	E1AP_DRB_Removed_Item__dRB_Released_In_Session_released_in_session	= 0,
	E1AP_DRB_Removed_Item__dRB_Released_In_Session_not_released_in_session	= 1
	/*
	 * Enumeration is extensible
	 */
} e_E1AP_DRB_Removed_Item__dRB_Released_In_Session;

/* Forward declarations */
struct E1AP_ProtocolExtensionContainer;
struct E1AP_QoS_Flow_Removed_Item;

/* E1AP_DRB-Removed-Item */
typedef struct E1AP_DRB_Removed_Item {
	E1AP_DRB_ID_t	 dRB_ID;
	long	*dRB_Released_In_Session;	/* OPTIONAL */
	OCTET_STRING_t	*dRB_Accumulated_Session_Time;	/* OPTIONAL */
	struct E1AP_DRB_Removed_Item__qoS_Flow_Removed_List {
		A_SEQUENCE_OF(struct E1AP_QoS_Flow_Removed_Item) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *qoS_Flow_Removed_List;
	struct E1AP_ProtocolExtensionContainer	*iE_Extensions;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Removed_Item_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_E1AP_dRB_Released_In_Session_3;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Removed_Item;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Removed_Item_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Removed_Item_1[5];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_DRB_Removed_Item_H_ */
#include <asn_internal.h>
