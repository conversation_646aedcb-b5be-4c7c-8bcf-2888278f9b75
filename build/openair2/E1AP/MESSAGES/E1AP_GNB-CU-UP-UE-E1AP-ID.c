/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_GNB-CU-UP-UE-E1AP-ID.h"

int
E1AP_GNB_CU_UP_UE_E1AP_ID_constraint(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	
	/* Constraint check succeeded */
	return 0;
}

/*
 * This type is implemented using NativeInteger,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_GNB_CU_UP_UE_E1AP_ID_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 32, -1,  0,  4294967295 }	/* (0..4294967295) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
const asn_INTEGER_specifics_t asn_SPC_E1AP_GNB_CU_UP_UE_E1AP_ID_specs_1 = {
	0,	0,	0,	0,	0,
	0,	/* Native long size */
	1	/* Unsigned representation */
};
static const ber_tlv_tag_t asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (2 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID = {
	"GNB-CU-UP-UE-E1AP-ID",
	"GNB-CU-UP-UE-E1AP-ID",
	&asn_OP_NativeInteger,
	asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1,
	sizeof(asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1)
		/sizeof(asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1[0]), /* 1 */
	asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1)
		/sizeof(asn_DEF_E1AP_GNB_CU_UP_UE_E1AP_ID_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_GNB_CU_UP_UE_E1AP_ID_constr_1,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		E1AP_GNB_CU_UP_UE_E1AP_ID_constraint
	},
	0, 0,	/* No members */
	&asn_SPC_E1AP_GNB_CU_UP_UE_E1AP_ID_specs_1	/* Additional specs */
};

