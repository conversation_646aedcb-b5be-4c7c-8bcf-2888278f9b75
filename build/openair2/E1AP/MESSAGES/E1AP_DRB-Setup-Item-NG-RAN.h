/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_DRB_Setup_Item_NG_RAN_H_
#define	_E1AP_DRB_Setup_Item_NG_RAN_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_DRB-ID.h"
#include "E1AP_UP-Parameters.h"
#include "E1AP_QoS-Flow-List.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct E1AP_Data_Forwarding_Information;
struct E1AP_QoS_Flow_Failed_List;
struct E1AP_ProtocolExtensionContainer;

/* E1AP_DRB-Setup-Item-NG-RAN */
typedef struct E1AP_DRB_Setup_Item_NG_RAN {
	E1AP_DRB_ID_t	 dRB_ID;
	struct E1AP_Data_Forwarding_Information	*dRB_data_Forwarding_Information_Response;	/* OPTIONAL */
	E1AP_UP_Parameters_t	 uL_UP_Transport_Parameters;
	E1AP_QoS_Flow_List_t	 flow_Setup_List;
	struct E1AP_QoS_Flow_Failed_List	*flow_Failed_List;	/* OPTIONAL */
	struct E1AP_ProtocolExtensionContainer	*iE_Extensions;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Setup_Item_NG_RAN_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Setup_Item_NG_RAN;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Setup_Item_NG_RAN_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Setup_Item_NG_RAN_1[6];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_DRB_Setup_Item_NG_RAN_H_ */
#include <asn_internal.h>
