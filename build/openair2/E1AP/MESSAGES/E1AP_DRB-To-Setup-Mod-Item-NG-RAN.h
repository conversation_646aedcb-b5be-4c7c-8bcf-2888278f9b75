/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_H_
#define	_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_DRB-ID.h"
#include "E1AP_SDAP-Configuration.h"
#include "E1AP_PDCP-Configuration.h"
#include "E1AP_Cell-Group-Information.h"
#include "E1AP_QoS-Flow-QoS-Parameter-List.h"
#include "E1AP_Inactivity-Timer.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct E1AP_Data_Forwarding_Information_Request;
struct E1AP_PDCP_SN_Status_Information;
struct E1AP_ProtocolExtensionContainer;

/* E1AP_DRB-To-Setup-Mod-Item-NG-RAN */
typedef struct E1AP_DRB_To_Setup_Mod_Item_NG_RAN {
	E1AP_DRB_ID_t	 dRB_ID;
	E1AP_SDAP_Configuration_t	 sDAP_Configuration;
	E1AP_PDCP_Configuration_t	 pDCP_Configuration;
	E1AP_Cell_Group_Information_t	 cell_Group_Information;
	E1AP_QoS_Flow_QoS_Parameter_List_t	 flow_Mapping_Information;
	struct E1AP_Data_Forwarding_Information_Request	*dRB_Data_Forwarding_Information_Request;	/* OPTIONAL */
	E1AP_Inactivity_Timer_t	*dRB_Inactivity_Timer;	/* OPTIONAL */
	struct E1AP_PDCP_SN_Status_Information	*pDCP_SN_Status_Information;	/* OPTIONAL */
	struct E1AP_ProtocolExtensionContainer	*iE_Extensions;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Setup_Mod_Item_NG_RAN_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Setup_Mod_Item_NG_RAN;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_1[9];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_DRB_To_Setup_Mod_Item_NG_RAN_H_ */
#include <asn_internal.h>
