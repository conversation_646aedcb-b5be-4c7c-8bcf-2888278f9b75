/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_DiscardTimer.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_DiscardTimer_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_E1AP_DiscardTimer_value2enum_1[] = {
	{ 0,	4,	"ms10" },
	{ 1,	4,	"ms20" },
	{ 2,	4,	"ms30" },
	{ 3,	4,	"ms40" },
	{ 4,	4,	"ms50" },
	{ 5,	4,	"ms60" },
	{ 6,	4,	"ms75" },
	{ 7,	5,	"ms100" },
	{ 8,	5,	"ms150" },
	{ 9,	5,	"ms200" },
	{ 10,	5,	"ms250" },
	{ 11,	5,	"ms300" },
	{ 12,	5,	"ms500" },
	{ 13,	5,	"ms750" },
	{ 14,	6,	"ms1500" },
	{ 15,	8,	"infinity" }
};
static const unsigned int asn_MAP_E1AP_DiscardTimer_enum2value_1[] = {
	15,	/* infinity(15) */
	0,	/* ms10(0) */
	7,	/* ms100(7) */
	8,	/* ms150(8) */
	14,	/* ms1500(14) */
	1,	/* ms20(1) */
	9,	/* ms200(9) */
	10,	/* ms250(10) */
	2,	/* ms30(2) */
	11,	/* ms300(11) */
	3,	/* ms40(3) */
	4,	/* ms50(4) */
	12,	/* ms500(12) */
	5,	/* ms60(5) */
	6,	/* ms75(6) */
	13	/* ms750(13) */
};
const asn_INTEGER_specifics_t asn_SPC_E1AP_DiscardTimer_specs_1 = {
	asn_MAP_E1AP_DiscardTimer_value2enum_1,	/* "tag" => N; sorted by tag */
	asn_MAP_E1AP_DiscardTimer_enum2value_1,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_E1AP_DiscardTimer_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_DiscardTimer = {
	"DiscardTimer",
	"DiscardTimer",
	&asn_OP_NativeEnumerated,
	asn_DEF_E1AP_DiscardTimer_tags_1,
	sizeof(asn_DEF_E1AP_DiscardTimer_tags_1)
		/sizeof(asn_DEF_E1AP_DiscardTimer_tags_1[0]), /* 1 */
	asn_DEF_E1AP_DiscardTimer_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_DiscardTimer_tags_1)
		/sizeof(asn_DEF_E1AP_DiscardTimer_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_DiscardTimer_constr_1,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_E1AP_DiscardTimer_specs_1	/* Additional specs */
};

