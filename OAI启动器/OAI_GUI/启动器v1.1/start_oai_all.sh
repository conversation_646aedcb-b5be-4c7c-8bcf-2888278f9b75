#!/bin/bash


GNB_DIR="/home/<USER>/testOai/oaiseu/cmake_targets/ran_build/build"
UE_DIR="/home/<USER>/testOai/oaiseu/cmake_targets/ran_build/build"
CN_DIR="/home/<USER>/oai-cn5g"
GNB_CONF="/home/<USER>/testOai/oaiseu/targets/PROJECTS/GENERIC-NR-5GC/CONF/gnb.sa.band77.162prb.usrpn310.4x4.conf"
UE_CONF="/home/<USER>/testOai/oaiseu/targets/PROJECTS/GENERIC-NR-5GC/CONF/ue.conf"
GNB_IP="**************"            # gNB 网卡IP（用于核心网路由）

echo "=== 启动 OAI 5G 系统 ==="

# 1. 首先启动核心网
echo "步骤 1: 启动 OAI CN5G 核心网..."
gnome-terminal --title="OAI CN5G Core Network" -- bash -c "\
cd $CN_DIR; \
echo '启动 OAI CN5G 核心网...'; \
docker compose up -d; \
echo '添加路由...'; \
echo '123' | sudo -S ip route add ********/24 via ************** 2>/dev/null || echo '路由已存在'; \
echo '等待核心网完全启动...'; \
echo '跟踪 oai-amf 日志...'; \
docker logs oai-amf -f; \
exec bash\
"

# 等待核心网启动
echo "等待核心网启动完成..."
# 2. 启动 gNB (修正参数)
echo "步骤 2: 启动 gNB RF模拟器..."
gnome-terminal --title="OAI gNB" -- bash -c "\
cd $GNB_DIR; \
echo '启动 gNB RF模拟器...'; \
echo '123' | sudo -S ./nr-softmodem -O $GNB_CONF --gNBs.[0].min_rxtxtime 6 --rfsim -d 2>&1 | tee /tmp/gnb_output.log ; \
exec bash\
"
# 等待gNB启动并连接到核心网
echo "等待gNB启动并连接到核心网..."
# 3. 启动 UE (修正参数)
echo "步骤 3: 启动 OAI nrUE..."
gnome-terminal --title="OAI nrUE" -- bash -c "\
cd $UE_DIR; \
echo '启动 OAI nrUE...'; \
echo '123' | sudo -S ./nr-uesoftmodem --rfsim --rfsimulator.serveraddr 127.0.0.1 -r 162 --numerology 1 --band 77 --ssb 852 -C 4100160000 -O $UE_CONF -d 2>&1 | tee /tmp/nrue_output.log ; \
exec bash\
"

echo ""
echo "=== 所有服务已启动 ==="
echo "请检查各个终端窗口的状态："
echo "1. CN5G 核心网: 应显示AMF日志"
echo "2. gNB: 应显示 'Connected' 状态"
echo "3. nrUE: 应显示注册成功和RSRP信号"
echo ""
echo "预期结果:"
echo "- gNB状态从 'Disconnected' 变为 'Connected'"
echo "- UE注册成功，状态为 '5GMM-REGISTERED'"
echo "- 创建 oaitun_ue1 网络接口"
echo "- 可以通过 'ping ************** -I oaitun_ue1' 测试连接"
echo ""
echo "验证命令:"
echo "1. 检查网络接口: ifconfig | grep oaitun"
echo "2. 测试连接: ping ************** -I oaitun_ue1 -c 3"
echo "3. 查看日志: tail -f /tmp/gnb_output.log"
echo "4. 查看日志: tail -f /tmp/nrue_output.log"

